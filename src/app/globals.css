@import './theme.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  * {
    @apply border-border;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .tool-card {
    @apply relative bg-card border border-border p-5 rounded-xl transition-all duration-300;
  }

  .tool-card:hover {
    @apply shadow-md border-primary/20 -translate-y-1;
  }

  .tool-card-featured {
    @apply relative bg-gradient-to-br from-accent to-background border border-primary/20 p-5 rounded-xl transition-all duration-300;
  }

  .tool-card-featured:hover {
    @apply shadow-lg border-primary/40 -translate-y-1;
  }

  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-new {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-featured {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .badge-free {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-premium {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .nav-link {
    @apply flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground;
  }

  .nav-link-active {
    @apply bg-accent text-accent-foreground;
  }

  .search-input {
    @apply w-full bg-background/70 backdrop-blur-sm rounded-full px-5 py-3 border border-border focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-base;
  }

  .glass-card {
    @apply bg-white/70 backdrop-blur-sm border border-white/20 shadow-sm dark:bg-gray-900/70 dark:border-gray-700/20;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md dark:bg-gray-900/80;
  }

  /* Enhanced theme-aware components */
  .theme-card {
    @apply bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .theme-gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300;
  }

  .theme-gradient-bg {
    @apply bg-gradient-to-b from-gray-50/50 to-white dark:from-gray-900/50 dark:to-background;
  }

  .theme-border {
    @apply border-gray-200 dark:border-gray-700;
  }

  .theme-text-primary {
    @apply text-gray-900 dark:text-white;
  }

  .theme-text-secondary {
    @apply text-gray-600 dark:text-gray-300;
  }

  .theme-text-muted {
    @apply text-gray-500 dark:text-gray-400;
  }

  /* Improved category button styles */
  .category-button {
    @apply flex items-center gap-2 px-4 py-3 rounded-xl border border-border bg-card hover:border-primary/30 hover:shadow-sm transition-all;
  }

  .category-button-active {
    @apply border-primary/50 bg-accent shadow-sm;
  }

  /* Better form controls */
  .form-input {
    @apply rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all;
  }

  /* Animation utilities */
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-md;
  }

  /* Header specific styles */
  .header-glass {
    @apply bg-white/80 backdrop-blur-md border-b border-gray-200/50 dark:bg-gray-900/80 dark:border-gray-700/50;
    @apply supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-gray-900/60;
  }

  .header-logo-gradient {
    @apply bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent;
    @apply hover:from-blue-600 hover:to-purple-600 transition-all duration-300;
  }

  .header-nav-link {
    @apply text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400;
    @apply transition-colors duration-200 font-medium;
  }

  .header-button-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700;
    @apply text-white shadow-lg hover:shadow-xl transition-all duration-200;
  }

  .header-user-avatar {
    @apply h-8 w-8 rounded-full object-cover ring-2 ring-transparent hover:ring-blue-200 dark:hover:ring-blue-800;
    @apply transition-all duration-200;
  }

  /* Accessibility improvements */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900;
  }

  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }
}
