@tailwind base;
@tailwind components;
@tailwind utilities;

:root  {
  /* ContentFlow Professional Theme - Light Mode */
  --background: 0 0% 100%;             /* Clean white background */
  --foreground: 222.2 84% 4.9%;        /* Dark text */
  --card: 0 0% 100%;                   /* White cards */
  --card-foreground: 222.2 84% 4.9%;   /* Dark text on cards */
  --popover: 0 0% 100%;                /* White popover */
  --popover-foreground: 222.2 84% 4.9%; /* Dark text on popover */
  --primary: 221.2 83.2% 53.3%;       /* Professional blue #1890FF */
  --primary-foreground: 210 40% 98%;   /* Light text on blue */
  --secondary: 210 40% 96%;            /* Light gray background */
  --secondary-foreground: 222.2 84% 4.9%; /* Dark text on light */
  --muted: 210 40% 96%;                /* Muted background */
  --muted-foreground: 215.4 16.3% 46.9%; /* Muted text */
  --accent: 210 40% 96%;               /* Accent background */
  --accent-foreground: 222.2 84% 4.9%; /* Dark text on accent */
  --destructive: 0 84.2% 60.2%;        /* Error red */
  --destructive-foreground: 210 40% 98%; /* Light text on red */
  --border: 214.3 31.8% 91.4%;        /* Light border */
  --input: 214.3 31.8% 91.4%;         /* Input border */
  --ring: 221.2 83.2% 53.3%;          /* Focus ring blue */
  --radius: 0.5rem;                    /* Standard border radius */

  /* ContentFlow-specific custom properties */
  --contentflow-blue: 221.2 83.2% 53.3%;    /* Primary blue #1890FF */
  --contentflow-success: 142.1 76.2% 36.3%; /* Success green #52C41A */
  --contentflow-warning: 45 93.4% 47.5%;    /* Warning yellow #FAAD14 */
  --contentflow-error: 0 84.2% 60.2%;       /* Error red #FF4D4F */
  --contentflow-gray: 210 40% 96%;          /* Background gray #F0F2F5 */
  --contentflow-text-gray: 0 0% 54.9%;     /* Text gray #8C8C8C */
}

.dark  {
  /* ContentFlow Professional Theme - Dark Mode */
  --background: 222.2 84% 4.9%;        /* Dark background */
  --foreground: 210 40% 98%;           /* Light text */
  --card: 222.2 84% 4.9%;              /* Dark cards */
  --card-foreground: 210 40% 98%;      /* Light text on cards */
  --popover: 222.2 84% 4.9%;           /* Dark popover */
  --popover-foreground: 210 40% 98%;   /* Light text on popover */
  --primary: 217.2 91.2% 59.8%;        /* Lighter blue for dark mode */
  --primary-foreground: 222.2 84% 4.9%; /* Dark text on blue */
  --secondary: 217.2 32.6% 17.5%;      /* Dark secondary */
  --secondary-foreground: 210 40% 98%; /* Light text on dark */
  --muted: 217.2 32.6% 17.5%;          /* Muted dark background */
  --muted-foreground: 215 20.2% 65.1%; /* Muted light text */
  --accent: 217.2 32.6% 17.5%;         /* Dark accent */
  --accent-foreground: 210 40% 98%;    /* Light text on dark accent */
  --destructive: 0 62.8% 30.6%;        /* Dark mode error red */
  --destructive-foreground: 210 40% 98%; /* Light text on red */
  --border: 217.2 32.6% 17.5%;         /* Dark border */
  --input: 217.2 32.6% 17.5%;          /* Dark input border */
  --ring: 217.2 91.2% 59.8%;           /* Focus ring blue */

  /* Dark mode ContentFlow colors */
  --contentflow-blue: 217.2 91.2% 59.8%;    /* Lighter blue for dark */
  --contentflow-success: 142.1 70.6% 45.3%; /* Success green for dark */
  --contentflow-warning: 45 93.4% 47.5%;    /* Warning yellow */
  --contentflow-error: 0 62.8% 30.6%;       /* Error red for dark */
  --contentflow-gray: 217.2 32.6% 17.5%;    /* Dark gray */
  --contentflow-text-gray: 215 20.2% 65.1%; /* Light gray text */
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .tool-card {
    @apply relative bg-card border border-border p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card:hover {
    @apply shadow-md border-primary/20 -translate-y-1;
  }

  .tool-card-featured {
    @apply relative bg-gradient-to-br from-accent to-background border border-primary/20 p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card-featured:hover {
    @apply shadow-lg border-primary/40 -translate-y-1;
  }

  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-new {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-featured {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .badge-free {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-premium {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .nav-link {
    @apply flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground;
  }

  .nav-link-active {
    @apply bg-accent text-accent-foreground;
  }

  .search-input {
    @apply w-full bg-background/70 backdrop-blur-sm rounded-full px-5 py-3 border border-border focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-base;
  }

  .glass-card {
    @apply bg-white/70 backdrop-blur-sm border border-white/20 shadow-sm dark:bg-gray-900/70;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md dark:bg-gray-900/80;
  }
  
  /* Improved category button styles */
  .category-button {
    @apply flex items-center gap-2 px-4 py-3 rounded-xl border border-sidebar-border bg-sidebar-background hover:bg-sidebar-hover hover:border-sidebar-primary/30 hover:shadow-sm transition-all;
  }
  
  .category-button-active {
    @apply border-sidebar-primary/50 bg-sidebar-accent shadow-sm;
  }
  
  /* Better form controls */
  .form-input {
    @apply rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all;
  }
  
  /* Animation utilities */
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }
  
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-md;
  }
  
  /* 侧边栏样式增强 */
  .sidebar-header {
    @apply border-b border-sidebar-border pb-3 mb-4;
  }
  
  .sidebar-title {
    @apply text-lg font-semibold text-sidebar-foreground;
  }
  
  .sidebar-category-icon {
    @apply text-sidebar-primary transition-colors;
  }
  
  .sidebar-category-name {
    @apply text-sidebar-foreground transition-colors;
  }
  
  .sidebar-category-item {
    @apply rounded-lg transition-all hover:bg-sidebar-hover p-2 flex items-center gap-3 cursor-pointer;
  }
  
  .sidebar-category-item-active {
    @apply bg-sidebar-accent text-sidebar-accent-foreground;
  }
}