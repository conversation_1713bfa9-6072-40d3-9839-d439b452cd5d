@tailwind base;
@tailwind components;
@tailwind utilities;

:root  {
  /* Modern Wedding Theme - Light Mode */
  --background: 350 20% 98%;           /* Soft ivory white */
  --foreground: 345 15% 15%;           /* Deep charcoal with warm undertones */
  --card: 350 30% 99%;                 /* Pure ivory for cards */
  --card-foreground: 345 15% 15%;      /* Consistent with foreground */
  --popover: 350 30% 99%;              /* Matching card background */
  --popover-foreground: 345 15% 15%;   /* Consistent text */
  --primary: 340 75% 75%;              /* Elegant rose pink */
  --primary-foreground: 350 30% 99%;   /* Ivory text on rose */
  --secondary: 345 25% 92%;            /* Soft blush */
  --secondary-foreground: 345 20% 25%; /* Darker warm gray */
  --muted: 345 20% 94%;                /* Very light warm gray */
  --muted-foreground: 345 15% 45%;     /* Medium warm gray */
  --accent: 340 60% 88%;               /* Light rose accent */
  --accent-foreground: 345 20% 25%;    /* Dark text on light accent */
  --destructive: 0 75% 65%;            /* Soft red for errors */
  --destructive-foreground: 350 30% 99%; /* Ivory text */
  --border: 345 20% 88%;               /* Soft border color */
  --input: 345 25% 92%;                /* Input background */
  --ring: 340 75% 75%;                 /* Focus ring matches primary */
  --radius: 0.75rem;                   /* Slightly more rounded for elegance */

  /* Wedding-specific custom properties */
  --wedding-rose: 340 75% 75%;         /* Primary rose */
  --wedding-blush: 345 60% 85%;        /* Soft blush */
  --wedding-cream: 45 40% 95%;         /* Warm cream */
  --wedding-gold: 45 85% 75%;          /* Elegant gold accent */
  --wedding-sage: 120 25% 75%;         /* Soft sage green */
  --wedding-lavender: 280 40% 85%;     /* Gentle lavender */
}

.dark  {
  /* Modern Wedding Theme - Dark Mode */
  --background: 345 15% 8%;            /* Deep warm charcoal */
  --foreground: 350 20% 95%;           /* Soft ivory text */
  --card: 345 20% 12%;                 /* Slightly lighter card background */
  --card-foreground: 350 20% 95%;      /* Consistent text */
  --popover: 345 20% 10%;              /* Dark popover */
  --popover-foreground: 350 20% 95%;   /* Light text */
  --primary: 340 70% 70%;              /* Slightly muted rose for dark mode */
  --primary-foreground: 345 15% 8%;    /* Dark text on rose */
  --secondary: 345 15% 18%;            /* Dark warm gray */
  --secondary-foreground: 350 20% 90%; /* Light text */
  --muted: 345 15% 15%;                /* Muted dark background */
  --muted-foreground: 345 10% 65%;     /* Medium gray text */
  --accent: 340 50% 25%;               /* Dark rose accent */
  --accent-foreground: 350 20% 95%;    /* Light text on dark accent */
  --destructive: 0 70% 60%;            /* Soft red for dark mode */
  --destructive-foreground: 350 20% 95%; /* Light text */
  --border: 345 15% 20%;               /* Dark border */
  --input: 345 15% 18%;                /* Dark input background */
  --ring: 340 70% 70%;                 /* Focus ring matches primary */

  /* Dark mode wedding colors */
  --wedding-rose: 340 70% 70%;         /* Muted rose for dark */
  --wedding-blush: 345 40% 30%;        /* Dark blush */
  --wedding-cream: 45 20% 20%;         /* Dark cream */
  --wedding-gold: 45 60% 60%;          /* Muted gold */
  --wedding-sage: 120 20% 30%;         /* Dark sage */
  --wedding-lavender: 280 30% 35%;     /* Dark lavender */
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .tool-card {
    @apply relative bg-card border border-border p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card:hover {
    @apply shadow-md border-primary/20 -translate-y-1;
  }

  .tool-card-featured {
    @apply relative bg-gradient-to-br from-accent to-background border border-primary/20 p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card-featured:hover {
    @apply shadow-lg border-primary/40 -translate-y-1;
  }

  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-new {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-featured {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .badge-free {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-premium {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .nav-link {
    @apply flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground;
  }

  .nav-link-active {
    @apply bg-accent text-accent-foreground;
  }

  .search-input {
    @apply w-full bg-background/70 backdrop-blur-sm rounded-full px-5 py-3 border border-border focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-base;
  }

  .glass-card {
    @apply bg-white/70 backdrop-blur-sm border border-white/20 shadow-sm dark:bg-gray-900/70;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md dark:bg-gray-900/80;
  }
  
  /* Improved category button styles */
  .category-button {
    @apply flex items-center gap-2 px-4 py-3 rounded-xl border border-sidebar-border bg-sidebar-background hover:bg-sidebar-hover hover:border-sidebar-primary/30 hover:shadow-sm transition-all;
  }
  
  .category-button-active {
    @apply border-sidebar-primary/50 bg-sidebar-accent shadow-sm;
  }
  
  /* Better form controls */
  .form-input {
    @apply rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all;
  }
  
  /* Animation utilities */
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }
  
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-md;
  }
  
  /* 侧边栏样式增强 */
  .sidebar-header {
    @apply border-b border-sidebar-border pb-3 mb-4;
  }
  
  .sidebar-title {
    @apply text-lg font-semibold text-sidebar-foreground;
  }
  
  .sidebar-category-icon {
    @apply text-sidebar-primary transition-colors;
  }
  
  .sidebar-category-name {
    @apply text-sidebar-foreground transition-colors;
  }
  
  .sidebar-category-item {
    @apply rounded-lg transition-all hover:bg-sidebar-hover p-2 flex items-center gap-3 cursor-pointer;
  }
  
  .sidebar-category-item-active {
    @apply bg-sidebar-accent text-sidebar-accent-foreground;
  }
}