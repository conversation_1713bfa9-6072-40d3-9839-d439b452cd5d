import { ContentFlowLandingPage } from "@/components/contentflow/LandingPage";
import Header from "@/components/Header";
import { setRequestLocale } from 'next-intl/server';
import { getMessages } from '@/i18n/routing';
import type { Metadata } from "next";

// Add page-specific metadata
export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const messages = await getMessages(locale);

  return {
    title: "ContentFlow - AI驱动的内容创作平台",
    description: "统一管理多个社交平台，AI智能优化内容，数据驱动决策。专为内容创作者打造的一站式解决方案。",
  };
}

export default async function HomePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 设置请求的 locale
  setRequestLocale(locale);

  // 获取国际化消息
  const messages = await getMessages(locale);

  return (
    <>
      <Header header={messages.header} />
      <ContentFlowLandingPage />
    </>
  );
}
