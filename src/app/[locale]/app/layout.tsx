'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter, usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { toast } from "sonner";
import { LoadingPage } from '@/components/ui/loading-spinner';
import { 
  PenTool, 
  BarChart3, 
  Calendar, 
  Settings, 
  Bell, 
  Search,
  User
} from 'lucide-react';

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState(() => {
    if (pathname.includes('/dashboard')) return 'dashboard';
    if (pathname.includes('/content')) return 'content';
    if (pathname.includes('/analytics')) return 'analytics';
    if (pathname.includes('/publish')) return 'publish';
    if (pathname.includes('/accounts')) return 'accounts';
    return 'dashboard';
  });

  // Show loading while checking authentication
  if (status === 'loading') {
    return <LoadingPage />;
  }

  // If not authenticated, the middleware will handle redirect
  if (status === 'unauthenticated') {
    return <LoadingPage />;
  }

  const handleLogout = async () => {
    try {
      await signOut({
        callbackUrl: '/zh',
        redirect: true
      });
      toast.success("已退出登录，感谢使用ContentFlow！");
    } catch (error) {
      toast.error("退出登录失败，请重试");
    }
  };

  // Get current locale from pathname
  const currentLocale = pathname.split('/')[1] || 'zh';

  const handleTabChange = (tab: string) => {
    console.log('Tab change clicked:', tab, 'Current locale:', currentLocale);
    setActiveTab(tab);
    switch (tab) {
      case 'dashboard':
        router.push(`/${currentLocale}/app/dashboard`);
        break;
      case 'content':
        router.push(`/${currentLocale}/app/content`);
        break;
      case 'analytics':
        router.push(`/${currentLocale}/app/analytics`);
        break;
      case 'publish':
        router.push(`/${currentLocale}/app/publish`);
        break;
      case 'accounts':
        router.push(`/${currentLocale}/app/accounts`);
        break;
    }
  };

  // Don't show header on auth pages (handled by main layout)
  if (pathname.includes('/auth/')) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Application Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-500 rounded-lg flex items-center justify-center shadow-lg">
                <PenTool className="w-4 h-4 text-white" />
              </div>
              <h1 className="text-xl font-bold contentflow-text-gradient">ContentFlow</h1>
            </div>
            <nav className="flex space-x-1" style={{ pointerEvents: 'auto', zIndex: 10 }}>
              <button
                onClick={() => {
                  console.log('Dashboard button clicked!');
                  handleTabChange('dashboard');
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === 'dashboard'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <BarChart3 className="w-4 h-4" />
                <span>概览</span>
              </button>
              <button
                onClick={() => {
                  console.log('Content button clicked!');
                  handleTabChange('content');
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === 'content'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <PenTool className="w-4 h-4" />
                <span>内容创作</span>
              </button>
              <button
                onClick={() => {
                  console.log('Analytics button clicked!');
                  handleTabChange('analytics');
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === 'analytics'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <BarChart3 className="w-4 h-4" />
                <span>数据分析</span>
              </button>
              <button
                onClick={() => {
                  console.log('Publish button clicked!');
                  handleTabChange('publish');
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === 'publish'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Calendar className="w-4 h-4" />
                <span>发布管理</span>
              </button>
              <button
                onClick={() => {
                  console.log('Accounts button clicked!');
                  handleTabChange('accounts');
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === 'accounts'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Settings className="w-4 h-4" />
                <span>账户管理</span>
              </button>
            </nav>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input placeholder="搜索..." className="pl-10 w-64" />
            </div>
            <Button variant="ghost" size="icon">
              <Bell className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={handleLogout}>
              <User className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>
        {children}
      </main>
    </div>
  );
}
