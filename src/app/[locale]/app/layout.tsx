'use client';

import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter, usePathname } from 'next/navigation';
import { toast } from "sonner";
import { 
  PenTool, 
  BarChart3, 
  Calendar, 
  Settings, 
  Bell, 
  Search,
  User
} from 'lucide-react';

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState(() => {
    if (pathname.includes('/dashboard')) return 'dashboard';
    if (pathname.includes('/content')) return 'content';
    if (pathname.includes('/analytics')) return 'analytics';
    if (pathname.includes('/publish')) return 'publish';
    if (pathname.includes('/accounts')) return 'accounts';
    return 'dashboard';
  });

  const handleLogout = () => {
    toast.success("已退出登录，感谢使用ContentFlow！");
    router.push('/zh');
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    switch (tab) {
      case 'dashboard':
        router.push('/zh/app/dashboard');
        break;
      case 'content':
        router.push('/zh/app/content');
        break;
      case 'analytics':
        router.push('/zh/app/analytics');
        break;
      case 'publish':
        router.push('/zh/app/publish');
        break;
      case 'accounts':
        router.push('/zh/app/accounts');
        break;
    }
  };

  // Don't show header on auth pages (handled by main layout)
  if (pathname.includes('/auth/')) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <PenTool className="w-4 h-4 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">ContentFlow</h1>
            </div>
            <nav className="flex space-x-1">
              <Button
                variant={activeTab === 'dashboard' ? 'default' : 'ghost'}
                onClick={() => handleTabChange('dashboard')}
                className="flex items-center space-x-2"
              >
                <BarChart3 className="w-4 h-4" />
                <span>概览</span>
              </Button>
              <Button
                variant={activeTab === 'content' ? 'default' : 'ghost'}
                onClick={() => handleTabChange('content')}
                className="flex items-center space-x-2"
              >
                <PenTool className="w-4 h-4" />
                <span>内容创作</span>
              </Button>
              <Button
                variant={activeTab === 'analytics' ? 'default' : 'ghost'}
                onClick={() => handleTabChange('analytics')}
                className="flex items-center space-x-2"
              >
                <BarChart3 className="w-4 h-4" />
                <span>数据分析</span>
              </Button>
              <Button
                variant={activeTab === 'publish' ? 'default' : 'ghost'}
                onClick={() => handleTabChange('publish')}
                className="flex items-center space-x-2"
              >
                <Calendar className="w-4 h-4" />
                <span>发布管理</span>
              </Button>
              <Button
                variant={activeTab === 'accounts' ? 'default' : 'ghost'}
                onClick={() => handleTabChange('accounts')}
                className="flex items-center space-x-2"
              >
                <Settings className="w-4 h-4" />
                <span>账户管理</span>
              </Button>
            </nav>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input placeholder="搜索..." className="pl-10 w-64" />
            </div>
            <Button variant="ghost" size="icon">
              <Bell className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={handleLogout}>
              <User className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>
        {children}
      </main>
    </div>
  );
}
