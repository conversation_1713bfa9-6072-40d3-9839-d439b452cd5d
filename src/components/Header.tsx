"use client";

import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import { usePathname, useRouter } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, PenTool, ChevronDown, User, LogOut, ShoppingBag } from "lucide-react";
import {
  She<PERSON>,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { signIn, signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useCallback } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

// Enhanced TypeScript interfaces for better type safety
interface NavigationItem {
  key: string;
  label: string;
  href: string;
}

interface HeaderProps {
  header: {
    logo: string;
    nav: {
      features: string;
      pricing: string;
      faq: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
      profile: string;
    };
  };
  className?: string;
}

export default function Header({ header, className }: HeaderProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const router = useRouter();
  const currentLocale = pathname.split('/')[1] || 'en';

  // Create navigation items from header data
  const navigationItems: NavigationItem[] = Object.entries(header.nav).map(([key, label]) => ({
    key,
    label,
    href: `#${key}`,
  }));

  // Improved locale switching with Next.js router
  const handleLocaleSwitch = useCallback((locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    router.push(newPathname);
  }, [pathname, currentLocale, router]);

  const handleGetStarted = useCallback(() => {
    router.push(`/${currentLocale}/app/login`);
  }, [router, currentLocale]);

  const handleSignOut = useCallback(() => {
    signOut({ callbackUrl: `/${currentLocale}` });
  }, [currentLocale]);

  return (
    <header className={cn(
      "h-full bg-white/80 backdrop-blur-md border-b border-gray-200/50 dark:bg-gray-900/80 dark:border-gray-700/50",
      "supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-gray-900/60",
      className
    )}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="flex items-center justify-between h-full">
          {/* Left: Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <PenTool className="w-6 h-6 text-white" />
            </div>
            <Link
              href={`/${currentLocale}`}
              className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent hover:from-blue-600 hover:to-purple-600 transition-all duration-300"
            >
              {header.logo}
            </Link>
          </div>

          {/* Center: Navigation Links - Desktop Only */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <a
                key={item.key}
                href={item.href}
                className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 font-medium"
              >
                {item.label}
              </a>
            ))}

            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                >
                  <Globe className="h-4 w-4" />
                  <span className="text-sm font-medium">{localeNames[currentLocale]}</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                {locales.map((locale) => (
                  <DropdownMenuItem
                    key={locale}
                    onClick={() => handleLocaleSwitch(locale)}
                    className={cn(
                      "cursor-pointer",
                      currentLocale === locale && "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                    )}
                  >
                    {localeNames[locale]}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Login/User Menu */}
            {session ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-10 w-10 rounded-full ring-2 ring-transparent hover:ring-blue-200 dark:hover:ring-blue-800 transition-all"
                  >
                    <span className="sr-only">Open user menu</span>
                    {session.user?.image ? (
                      <Image
                        className="h-8 w-8 rounded-full object-cover"
                        src={session.user.image}
                        alt={session.user.name || 'User avatar'}
                        width={32}
                        height={32}
                        unoptimized
                        priority
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {session.user?.name?.charAt(0)?.toUpperCase() || 'U'}
                        </span>
                      </div>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <div className="px-3 py-2">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {session.user?.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {session.user?.email}
                    </p>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="flex items-center space-x-2 cursor-pointer"
                    >
                      <ShoppingBag className="h-4 w-4" />
                      <span>{header.userMenu.myOrders}</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/${currentLocale}/profile`}
                      className="flex items-center space-x-2 cursor-pointer"
                    >
                      <User className="h-4 w-4" />
                      <span>{header.userMenu.profile}</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleSignOut}
                    className="flex items-center space-x-2 cursor-pointer text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>{header.userMenu.signOut}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-3">
                <Button
                  variant="ghost"
                  onClick={() => signIn()}
                  className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  {header.cta.login}
                </Button>
                <Button
                  onClick={handleGetStarted}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {header.cta.signup}
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                >
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <SheetHeader>
                  <SheetTitle className="text-left flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                      <PenTool className="w-4 h-4 text-white" />
                    </div>
                    <span className="bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                      {header.logo}
                    </span>
                  </SheetTitle>
                </SheetHeader>
                <div className="flex flex-col space-y-6 mt-8">
                  {/* Navigation Links */}
                  <nav className="space-y-2">
                    {navigationItems.map((item) => (
                      <a
                        key={item.key}
                        href={item.href}
                        className="block px-3 py-2 text-lg font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors"
                      >
                        {item.label}
                      </a>
                    ))}
                  </nav>

                  {/* Language Switcher */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3 flex items-center space-x-2">
                      <Globe className="h-4 w-4" />
                      <span>Language</span>
                    </h3>
                    <div className="space-y-1">
                      {locales.map((locale) => (
                        <button
                          key={locale}
                          onClick={() => handleLocaleSwitch(locale)}
                          className={cn(
                            "block w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
                            currentLocale === locale
                              ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 font-medium"
                              : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                          )}
                        >
                          {localeNames[locale]}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* User Authentication Section */}
                  {session ? (
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                      <div className="flex items-center space-x-3 mb-4">
                        {session.user?.image ? (
                          <Image
                            className="h-10 w-10 rounded-full object-cover"
                            src={session.user.image}
                            alt={session.user.name || 'User avatar'}
                            width={40}
                            height={40}
                            unoptimized
                            priority
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <span className="text-white font-medium">
                              {session.user?.name?.charAt(0)?.toUpperCase() || 'U'}
                            </span>
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {session.user?.name}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {session.user?.email}
                          </p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Link
                          href={`/${currentLocale}/orders`}
                          className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
                        >
                          <ShoppingBag className="h-4 w-4" />
                          <span>{header.userMenu.myOrders}</span>
                        </Link>
                        <Link
                          href={`/${currentLocale}/profile`}
                          className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
                        >
                          <User className="h-4 w-4" />
                          <span>{header.userMenu.profile}</span>
                        </Link>
                        <button
                          type="button"
                          onClick={handleSignOut}
                          className="flex items-center space-x-3 w-full px-3 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
                        >
                          <LogOut className="h-4 w-4" />
                          <span>{header.userMenu.signOut}</span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                      <div className="space-y-3">
                        <Button
                          onClick={() => signIn()}
                          variant="outline"
                          className="w-full"
                        >
                          {header.cta.login}
                        </Button>
                        <Button
                          onClick={handleGetStarted}
                          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg"
                        >
                          {header.cta.signup}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}