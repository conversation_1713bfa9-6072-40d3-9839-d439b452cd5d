"use client";

import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import { usePathname, useRouter } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, PenTool, ChevronDown, User, LogOut, ShoppingBag } from "lucide-react";
import {
  She<PERSON>,
  <PERSON>etContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useCallback } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

// Enhanced TypeScript interfaces for better type safety
interface NavigationItem {
  key: string;
  label: string;
  href: string;
}

interface HeaderProps {
  header: {
    logo: string;
    nav: {
      features: string;
      pricing: string;
      faq: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
      profile: string;
    };
  };
  className?: string;
}

export default function Header({ header, className }: HeaderProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const router = useRouter();
  const currentLocale = pathname.split('/')[1] || 'en';

  // Create navigation items from header data
  const navigationItems: NavigationItem[] = Object.entries(header.nav).map(([key, label]) => ({
    key,
    label,
    href: `#${key}`,
  }));

  // Improved locale switching with Next.js router
  const handleLocaleSwitch = useCallback((locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    router.push(newPathname);
  }, [pathname, currentLocale, router]);

  const handleGetStarted = useCallback(() => {
    // Redirect to proper NextAuth signin page instead of custom login
    router.push(`/${currentLocale}/auth/signin`);
  }, [router, currentLocale]);

  const handleSignOut = useCallback(() => {
    signOut({ callbackUrl: `/${currentLocale}` });
  }, [currentLocale]);

  return (
    <header className={cn(
      "h-full wedding-glass-effect border-b border-rose-200/30 dark:border-rose-800/30",
      "supports-[backdrop-filter]:bg-white/50 dark:supports-[backdrop-filter]:bg-gray-900/50",
      className
    )}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="flex items-center justify-between h-full">
          {/* Left: Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-rose-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300">
              <PenTool className="w-6 h-6 text-white" />
            </div>
            <Link
              href={`/${currentLocale}`}
              className="text-2xl font-bold wedding-text-gradient hover:scale-105 transition-all duration-300"
            >
              {header.logo}
            </Link>
          </div>

          {/* Center: Navigation Links - Desktop Only */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <a
                key={item.key}
                href={item.href}
                className="text-rose-700 dark:text-rose-300 hover:text-rose-500 dark:hover:text-rose-400 transition-colors duration-200 font-medium hover:scale-105"
              >
                {item.label}
              </a>
            ))}

            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center space-x-2 text-rose-700 dark:text-rose-300 hover:text-rose-500 dark:hover:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20"
                >
                  <Globe className="h-4 w-4" />
                  <span className="text-sm font-medium">{localeNames[currentLocale]}</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40 wedding-card">
                {locales.map((locale) => (
                  <DropdownMenuItem
                    key={locale}
                    onClick={() => handleLocaleSwitch(locale)}
                    className={cn(
                      "cursor-pointer",
                      currentLocale === locale && "bg-rose-50 dark:bg-rose-900/20 text-rose-600 dark:text-rose-400"
                    )}
                  >
                    {localeNames[locale]}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Login/User Menu */}
            {session ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-10 w-10 rounded-full ring-2 ring-transparent hover:ring-rose-200 dark:hover:ring-rose-800 transition-all"
                  >
                    <span className="sr-only">Open user menu</span>
                    {session.user?.image ? (
                      <Image
                        className="h-8 w-8 rounded-full object-cover"
                        src={session.user.image}
                        alt={session.user.name || 'User avatar'}
                        width={32}
                        height={32}
                        unoptimized
                        priority
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-gradient-to-br from-rose-500 to-pink-500 flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {session.user?.name?.charAt(0)?.toUpperCase() || 'U'}
                        </span>
                      </div>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 wedding-card">
                  <div className="px-3 py-2">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {session.user?.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {session.user?.email}
                    </p>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="flex items-center space-x-2 cursor-pointer"
                    >
                      <ShoppingBag className="h-4 w-4" />
                      <span>{header.userMenu.myOrders}</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/${currentLocale}/profile`}
                      className="flex items-center space-x-2 cursor-pointer"
                    >
                      <User className="h-4 w-4" />
                      <span>{header.userMenu.profile}</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleSignOut}
                    className="flex items-center space-x-2 cursor-pointer text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>{header.userMenu.signOut}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-3">
                <Button
                  variant="ghost"
                  onClick={() => router.push(`/${currentLocale}/auth/signin`)}
                  className="text-rose-700 dark:text-rose-300 hover:text-rose-500 dark:hover:text-rose-400"
                >
                  {header.cta.login}
                </Button>
                <Button
                  onClick={handleGetStarted}
                  className="wedding-button-primary"
                >
                  {header.cta.signup}
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 text-rose-700 dark:text-rose-300 hover:text-rose-500 dark:hover:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20"
                >
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80 wedding-glass-effect">
                <SheetHeader>
                  <SheetTitle className="text-left flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-rose-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg">
                      <PenTool className="w-4 h-4 text-white" />
                    </div>
                    <span className="wedding-text-gradient">
                      {header.logo}
                    </span>
                  </SheetTitle>
                </SheetHeader>
                <div className="flex flex-col space-y-6 mt-8">
                  {/* Navigation Links */}
                  <nav className="space-y-2">
                    {navigationItems.map((item) => (
                      <a
                        key={item.key}
                        href={item.href}
                        className="block px-3 py-2 text-lg font-medium text-rose-800 dark:text-rose-200 hover:text-rose-600 dark:hover:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20 rounded-md transition-colors"
                      >
                        {item.label}
                      </a>
                    ))}
                  </nav>

                  {/* Language Switcher */}
                  <div className="border-t border-rose-200 dark:border-rose-700 pt-6">
                    <h3 className="text-sm font-medium text-rose-600 dark:text-rose-400 mb-3 flex items-center space-x-2">
                      <Globe className="h-4 w-4" />
                      <span>Language</span>
                    </h3>
                    <div className="space-y-1">
                      {locales.map((locale) => (
                        <button
                          key={locale}
                          onClick={() => handleLocaleSwitch(locale)}
                          className={cn(
                            "block w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
                            currentLocale === locale
                              ? "bg-rose-50 dark:bg-rose-900/20 text-rose-600 dark:text-rose-400 font-medium"
                              : "text-rose-700 dark:text-rose-300 hover:bg-rose-100 dark:hover:bg-rose-800"
                          )}
                        >
                          {localeNames[locale]}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* User Authentication Section */}
                  {session ? (
                    <div className="border-t border-rose-200 dark:border-rose-700 pt-6">
                      <div className="flex items-center space-x-3 mb-4">
                        {session.user?.image ? (
                          <Image
                            className="h-10 w-10 rounded-full object-cover"
                            src={session.user.image}
                            alt={session.user.name || 'User avatar'}
                            width={40}
                            height={40}
                            unoptimized
                            priority
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-rose-500 to-pink-500 flex items-center justify-center">
                            <span className="text-white font-medium">
                              {session.user?.name?.charAt(0)?.toUpperCase() || 'U'}
                            </span>
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {session.user?.name}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {session.user?.email}
                          </p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Link
                          href={`/${currentLocale}/orders`}
                          className="flex items-center space-x-3 px-3 py-2 text-sm text-rose-700 dark:text-rose-300 hover:bg-rose-100 dark:hover:bg-rose-800 rounded-md transition-colors"
                        >
                          <ShoppingBag className="h-4 w-4" />
                          <span>{header.userMenu.myOrders}</span>
                        </Link>
                        <Link
                          href={`/${currentLocale}/profile`}
                          className="flex items-center space-x-3 px-3 py-2 text-sm text-rose-700 dark:text-rose-300 hover:bg-rose-100 dark:hover:bg-rose-800 rounded-md transition-colors"
                        >
                          <User className="h-4 w-4" />
                          <span>{header.userMenu.profile}</span>
                        </Link>
                        <button
                          type="button"
                          onClick={handleSignOut}
                          className="flex items-center space-x-3 w-full px-3 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
                        >
                          <LogOut className="h-4 w-4" />
                          <span>{header.userMenu.signOut}</span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="border-t border-rose-200 dark:border-rose-700 pt-6">
                      <div className="space-y-3">
                        <Button
                          onClick={() => router.push(`/${currentLocale}/auth/signin`)}
                          variant="outline"
                          className="w-full wedding-button-secondary"
                        >
                          {header.cta.login}
                        </Button>
                        <Button
                          onClick={handleGetStarted}
                          className="w-full wedding-button-primary"
                        >
                          {header.cta.signup}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}