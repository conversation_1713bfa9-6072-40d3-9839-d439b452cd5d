"use client";

import { motion } from "framer-motion";
import { Star } from "lucide-react";

interface TestimonialProps {
  section: {
    title: string;
    subtitle: string;
    testimonials: Array<{
      content: string;
      author: {
        name: string;
        title: string;
        company: string;
        image: string;
      };
    }>;
  };
}

import { TestimonialsSection } from "@/components/blocks/testimonials-with-marquee";

export function Testimonial({ section }: TestimonialProps) {
  // Convert section testimonials to the format expected by TestimonialsSection
  const testimonials = section.testimonials.map((testimonial) => ({
    author: {
      name: testimonial.author.name,
      handle: `@${testimonial.author.name.toLowerCase().replace(/\s+/g, '')}`,
      avatar: testimonial.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(testimonial.author.name)}&background=1890FF&color=fff`
    },
    text: testimonial.content,
    href: "#"
  }));

  return (
    <section id="testimonials" className="contentflow-section-bg">
      <TestimonialsSection
        title={section.title}
        description={section.subtitle}
        testimonials={testimonials}
      />
    </section>
  );
}