'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useRouter } from 'next/navigation';
import { 
  PenTool, 
  BarChart3, 
  Calendar, 
  Target,
  Users,
  TrendingUp,
  Check,
  Star,
  ArrowRight,
  PlayCircle
} from 'lucide-react';

export const ContentFlowLandingPage = () => {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/zh/auth/signin');
  };

  return (
    <div className="min-h-screen wedding-hero-bg">

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <Badge className="mb-4 bg-rose-100 text-rose-800 hover:bg-rose-100 border-rose-200">
              <Star className="w-4 h-4 mr-1" />
              专为婚礼策划师打造的内容平台
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 wedding-text-gradient">
              让婚礼内容创作变得
              <span className="block mt-2"> 浪漫而高效</span>
            </h1>
            <p className="text-xl text-rose-700 dark:text-rose-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              专为婚礼策划师和新人打造的一站式内容管理平台。
              AI智能优化婚礼内容，数据驱动决策，让每个爱情故事都完美呈现。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleGetStarted}
                size="lg"
                className="wedding-button-primary text-lg px-8 py-4 rounded-full"
              >
                开始您的婚礼之旅
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="wedding-button-secondary text-lg px-8 py-4 rounded-full"
              >
                <PlayCircle className="w-5 h-5 mr-2" />
                观看浪漫演示
              </Button>
            </div>
            <div className="mt-12 flex items-center justify-center space-x-8 text-sm text-rose-600 dark:text-rose-400">
              <div className="flex items-center">
                <Check className="w-4 h-4 text-rose-500 mr-2" />
                14天免费体验
              </div>
              <div className="flex items-center">
                <Check className="w-4 h-4 text-rose-500 mr-2" />
                专业婚礼模板
              </div>
              <div className="flex items-center">
                <Check className="w-4 h-4 text-rose-500 mr-2" />
                AI智能优化
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 wedding-section-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 wedding-text-gradient">
              为什么选择 ContentFlow 婚礼版？
            </h2>
            <p className="text-xl text-rose-700 dark:text-rose-300 max-w-2xl mx-auto">
              专为婚礼行业打造的AI技术，让每个爱情故事都能完美呈现
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="wedding-card p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-rose-100 rounded-lg flex items-center justify-center mb-4">
                <PenTool className="w-6 h-6 text-rose-600" />
              </div>
              <h3 className="text-xl font-semibold text-rose-800 dark:text-rose-200 mb-2">AI婚礼文案</h3>
              <p className="text-rose-600 dark:text-rose-400">
                专为婚礼场景优化的AI文案生成，创造浪漫动人的婚礼故事和邀请函内容
              </p>
            </Card>

            <Card className="wedding-card p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mb-4">
                <Target className="w-6 h-6 text-pink-600" />
              </div>
              <h3 className="text-xl font-semibold text-rose-800 dark:text-rose-200 mb-2">婚礼平台管理</h3>
              <p className="text-rose-600 dark:text-rose-400">
                统一管理婚礼相关的社交媒体平台，从筹备到庆典，全程记录爱情时光
              </p>
            </Card>

            <Card className="wedding-card p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-rose-200 rounded-lg flex items-center justify-center mb-4">
                <BarChart3 className="w-6 h-6 text-rose-700" />
              </div>
              <h3 className="text-xl font-semibold text-rose-800 dark:text-rose-200 mb-2">婚礼数据洞察</h3>
              <p className="text-rose-600 dark:text-rose-400">
                分析婚礼内容表现，优化宾客互动，让每个婚礼时刻都获得最佳反响
              </p>
            </Card>

            <Card className="wedding-card p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-rose-300 rounded-lg flex items-center justify-center mb-4">
                <Calendar className="w-6 h-6 text-rose-800" />
              </div>
              <h3 className="text-xl font-semibold text-rose-800 dark:text-rose-200 mb-2">婚礼时间轴</h3>
              <p className="text-rose-600 dark:text-rose-400">
                智能规划婚礼筹备时间线，确保每个重要时刻都能完美记录和分享
              </p>
            </Card>

            <Card className="wedding-card p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-pink-200 rounded-lg flex items-center justify-center mb-4">
                <TrendingUp className="w-6 h-6 text-pink-700" />
              </div>
              <h3 className="text-xl font-semibold text-rose-800 dark:text-rose-200 mb-2">情感分析</h3>
              <p className="text-rose-600 dark:text-rose-400">
                AI分析宾客反馈和互动情感，优化婚礼内容，创造更多感动时刻
              </p>
            </Card>

            <Card className="wedding-card p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-rose-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-rose-600" />
              </div>
              <h3 className="text-xl font-semibold text-rose-800 dark:text-rose-200 mb-2">婚礼团队协作</h3>
              <p className="text-rose-600 dark:text-rose-400">
                新人、策划师、摄影师无缝协作，共同打造完美婚礼内容体验
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 wedding-section-bg">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 wedding-text-gradient">
              常见问题
            </h2>
            <p className="text-xl text-rose-700 dark:text-rose-300">
              解答您关于ContentFlow婚礼版的疑问
            </p>
          </div>

          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger className="text-left">
                ContentFlow支持哪些社交媒体平台？
              </AccordionTrigger>
              <AccordionContent>
                目前支持Instagram、TikTok、LinkedIn和Twitter等主流平台。我们正在不断添加更多平台支持，包括YouTube、小红书等。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2">
              <AccordionTrigger className="text-left">
                AI文案生成的质量如何？
              </AccordionTrigger>
              <AccordionContent>
                我们使用最新的GPT-4模型，结合您的品牌声音和历史数据训练，生成的文案经过优化，平均可提升30%的互动率。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-3">
              <AccordionTrigger className="text-left">
                免费版本有什么限制？
              </AccordionTrigger>
              <AccordionContent>
                免费版支持连接2个社交账户，每月发布50条内容，包含基础数据分析功能。升级到付费版本可享受更多功能和无限制使用。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-4">
              <AccordionTrigger className="text-left">
                数据安全如何保障？
              </AccordionTrigger>
              <AccordionContent>
                我们采用银行级加密技术，所有数据传输使用SSL加密，严格遵守GDPR等数据保护法规，绝不会泄露或滥用您的数据。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-5">
              <AccordionTrigger className="text-left">
                可以随时取消订阅吗？
              </AccordionTrigger>
              <AccordionContent>
                当然可以。您可以随时在账户设置中取消订阅，取消后仍可使用到当前计费周期结束。我们不收取任何取消费用。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-6">
              <AccordionTrigger className="text-left">
                是否提供技术支持？
              </AccordionTrigger>
              <AccordionContent>
                我们提供全天候客服支持，包括在线聊天、邮件支持和视频教程。付费用户还可享受优先技术支持服务。
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            准备好提升您的内容创作了吗？
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            加入成千上万的内容创作者，开始您的ContentFlow之旅
          </p>
          <Button
            onClick={handleGetStarted}
            size="lg"
            className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4"
          >
            立即免费注册
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <PenTool className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-xl font-bold">ContentFlow</h3>
              </div>
              <p className="text-gray-400">
                AI驱动的内容创作与发布管理平台
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">产品</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">功能特色</a></li>
                <li><a href="#" className="hover:text-white transition-colors">定价方案</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API文档</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">支持</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">帮助中心</a></li>
                <li><a href="#" className="hover:text-white transition-colors">联系我们</a></li>
                <li><a href="#" className="hover:text-white transition-colors">状态页面</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">公司</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">关于我们</a></li>
                <li><a href="#" className="hover:text-white transition-colors">隐私政策</a></li>
                <li><a href="#" className="hover:text-white transition-colors">服务条款</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 ContentFlow. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};
