'use client';

import React from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PenTool, Send, MessageCircle, Globe, Mail, Phone } from 'lucide-react';

export const ContentFlowFooter = () => {
  return (
    <footer className="bg-gray-900 text-white py-12 contentflow-footer">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 contentflow-button-primary rounded-lg flex items-center justify-center">
                <PenTool className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-xl font-bold contentflow-text-gradient">ContentFlow</h3>
            </div>
            <p className="text-gray-400 mb-6">
              AI驱动的内容创作与发布管理平台，让内容创作变得简单高效。
            </p>
            
            {/* Newsletter Signup */}
            <div className="mb-6">
              <h4 className="text-sm font-semibold mb-3 text-gray-300">订阅我们的更新</h4>
              <div className="flex">
                <Input 
                  type="email" 
                  placeholder="输入您的邮箱" 
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-400 rounded-r-none"
                />
                <Button className="contentflow-button-primary rounded-l-none">
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Contact Links */}
            <div className="flex space-x-3">
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-blue-400 hover:bg-blue-900/20 transition-colors">
                <MessageCircle className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-blue-400 hover:bg-blue-900/20 transition-colors">
                <Mail className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-blue-400 hover:bg-blue-900/20 transition-colors">
                <Phone className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-blue-400 hover:bg-blue-900/20 transition-colors">
                <Globe className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">产品</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#features" className="hover:text-white transition-colors">功能特色</a></li>
              <li><a href="#pricing" className="hover:text-white transition-colors">定价方案</a></li>
              <li><a href="/api" className="hover:text-white transition-colors">API文档</a></li>
              <li><a href="/integrations" className="hover:text-white transition-colors">集成应用</a></li>
              <li><a href="/templates" className="hover:text-white transition-colors">内容模板</a></li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">支持</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="/help" className="hover:text-white transition-colors">帮助中心</a></li>
              <li><a href="/tutorials" className="hover:text-white transition-colors">使用教程</a></li>
              <li><a href="/community" className="hover:text-white transition-colors">社区论坛</a></li>
              <li><a href="/contact" className="hover:text-white transition-colors">联系我们</a></li>
              <li><a href="/status" className="hover:text-white transition-colors">服务状态</a></li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">公司</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="/about" className="hover:text-white transition-colors">关于我们</a></li>
              <li><a href="/careers" className="hover:text-white transition-colors">招聘信息</a></li>
              <li><a href="/blog" className="hover:text-white transition-colors">博客</a></li>
              <li><a href="/press" className="hover:text-white transition-colors">媒体报道</a></li>
              <li><a href="/partners" className="hover:text-white transition-colors">合作伙伴</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm mb-4 md:mb-0">
              &copy; 2024 ContentFlow. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm text-gray-400">
              <a href="/privacy" className="hover:text-white transition-colors">隐私政策</a>
              <a href="/terms" className="hover:text-white transition-colors">服务条款</a>
              <a href="/cookies" className="hover:text-white transition-colors">Cookie政策</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
